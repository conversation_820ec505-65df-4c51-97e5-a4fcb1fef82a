### **"优约"通用预约小程序 & 管理后台 - 产品需求文档 (PRD) V1.3**

| **文档版本** | V1.3 | **创建日期** | 2023-10-27 |
| :--- | :--- | :--- | :--- |
| **创建人** | RHYS.AI产品团队 | **项目状态** | V1.2+增强版 |
| **变更记录** | V1.2: 1. 移除小程序端B端管理功能，聚焦C端体验。 <br> 2. 预约流程增加"自动/人工确认"的可配置模式。 <br> 3. 明确并发控制的技术方案。 <br><br> **V1.3: 1. 新增智能异常处理机制，提升系统稳定性。 <br> 2. 集成消息通知系统，支持多渠道智能推送。 <br> 3. 建立业务指标监控体系，实现数据驱动运营。 <br> 4. 全面优化用户体验设计，提升用户满意度。** |

---

#### **1. 项目概述**

##### **1.1 项目背景与目标**
本项目旨在开发一个以Web后台为配置中心、小程序为用户触点的通用预约解决方案。商家通过Web后台进行业务配置和数据管理，C端用户则通过小程序完成无缝的预约体验。

*   **产品目标：** 实现一个配置灵活、流程自动化、前后端分离的通用预约SaaS雏形。
*   **核心价值：** 一次配置，多端生效，为商家提供从业务设置到日常运营的全流程数字化工具。

##### **1.2 项目范围 (V1.2)**
*   **包含 (In Scope):**
    *   **C端用户小程序 (uni-app):** 纯粹的消费者预约工具。
    *   **B端Web管理后台 (Vue + Element Plus):** 系统唯一的、功能完备的管理与配置中心。
    *   **统一的后端API服务 (Spring Boot):** 为双端提供统一、安全的API。
*   **不包含 (Out of Scope):**
    *   多商户入驻、在线支付、复杂营销功能。

---

#### **2. 产品架构与角色**

##### **2.1 产品构成**
1.  **小程序端 (uni-app):** **唯一的职责是面向C端消费者**，提供服务浏览、预约及个人订单管理功能。
2.  **Web管理后台 (Vue + Element Plus):** **唯一的管理平台**，面向B端商家，负责所有业务配置、数据管理与分析。**建议采用响应式设计**，以便在移动设备浏览器上进行应急访问。
3.  **后端服务 (Spring Boot):** 统一的API服务中心。

##### **2.2 用户角色**

| 角色 | 使用平台 | 核心诉求 |
| :--- | :--- | :--- |
| **C端用户 (消费者)** | 小程序 | 流程简单、反馈即时、方便快捷。 |
| **B端管理员 (商家)** | Web管理后台 | 配置灵活、功能强大、掌控全局、数据清晰。 |

---

#### **3. 功能需求详述 (Functional Requirements)**

##### **3.1 小程序端 (uni-app) - C端用户功能**

*   **3.1.1 授权与登录:** （同上）微信授权登录，获取Token。
*   **3.1.2 首页 / 服务列表页:** （同上）动态展示由Web后台配置的服务项目。
*   **3.1.3 服务详情与预约日历页:** （同上）查看服务详情，并使用可视化日历选择可用时间段。
*   **3.1.4 提交预约与结果页:**
    *   **需求：** 用户确认信息并提交预约。
    *   **逻辑：** 提交预约后，根据API返回的最终状态，向用户展示不同的结果。
        *   若为“自动确认”模式，则提示：“**预约成功！**”
        *   若为“人工确认”模式，则提示：“**预约已提交，等待商家确认。**”
*   **3.1.5 个人中心 ("我的"):**
    *   **我的预约：**
        *   Tab切换按状态（`待确认`、`预约成功`、`已完成`、`已取消`）查看列表。
        *   在商家设定的规则内，可执行"取消预约"操作。
    *   **无"管理员入口"**。
*   **【V1.3新增】3.1.6 智能异常处理系统:**
    *   **网络异常处理：**
        *   自动检测网络状态，断网时显示友好提示。
        *   网络恢复后自动重试上次操作。
        *   支持离线浏览已缓存的服务信息。
    *   **预约冲突处理：**
        *   时段已满时自动推荐相邻可用时间。
        *   提供候补排队功能。
        *   智能推荐相似服务的可用时段。
    *   **操作异常恢复：**
        *   预约流程中断时自动保存进度。
        *   重新进入时提示恢复未完成操作。
        *   支持预约草稿功能。
*   **【V1.3新增】3.1.7 智能消息通知系统:**
    *   **微信模板消息：**
        *   预约确认通知（包含服务详情、时间地点）。
        *   预约提醒通知（服务前24小时和2小时）。
        *   状态变更通知（确认、取消、完成）。
    *   **个性化通知：**
        *   根据用户偏好自动调整通知时间。
        *   天气提醒（恶劣天气时的贴心提示）。
        *   个性化服务推荐。
    *   **通知偏好设置：**
        *   用户可自定义接收通知的类型和时间。
        *   支持通知免打扰时段设置。

##### **3.2 Web管理后台 (Vue + Element Plus) - B端商家功能**

*   **3.2.1 登录:** （同上）账号密码登录。
*   **3.2.2 仪表盘 (Dashboard):** （同上）核心业务数据概览。
*   **3.2.3 **核心：**店铺与预约设置**
    *   **店铺信息管理：** （同上）配置店铺基础信息。
    *   **预约规则配置 (重点):**
        *   **营业时间设置：** （同上）分周、分时段设置营业时间。
        *   **排班规则设置：** （同上）服务单位时长、服务间隔。
        *   **预约策略设置：**
            *   最多可提前N天预约。
            *   至少需提前M小时预约。
            *   预约取消时限。
            *   **【新增】预约确认模式 (单选框):**
                *   `()` **自动确认：** 用户提交预约后，系统自动占用库存并确认订单。（**推荐默认项**）
                *   `()` **需人工确认：** 用户提交预约后，订单状态为“待确认”，需管理员在后台手动确认。
*   **3.2.4 服务管理:** （同上）完整的服务项目CRUD，支持富文本编辑详情。
*   **3.2.5 预约管理:** （同上）强大的预约列表，支持多条件组合查询、导出，并可执行【确认】、【完成】、【取消】等操作。
*   **3.2.6 用户管理:** （同上）查看用户列表，可进行搜索和"拉黑"操作。
*   **【V1.3新增】3.2.7 消息通知管理:**
    *   **通知模板配置：**
        *   自定义预约确认、提醒、取消等通知内容。
        *   支持添加商家个性化信息和品牌元素。
        *   预览通知效果，支持A/B测试不同模板。
    *   **通知发送管理：**
        *   查看通知发送历史和送达率统计。
        *   支持手动发送特定通知给指定用户。
        *   批量通知功能（如店铺公告、服务变更等）。
    *   **通知效果分析：**
        *   通知打开率、点击率等关键指标监控。
        *   用户通知偏好分析和优化建议。
*   **【V1.3新增】3.2.8 数据分析与监控:**
    *   **实时业务监控：**
        *   预约转化率、用户活跃度等核心指标实时显示。
        *   异常数据自动预警（如预约失败率突增）。
        *   系统性能监控（响应时间、错误率等）。
    *   **业务分析报表：**
        *   用户行为分析（访问路径、使用习惯等）。
        *   服务热门度分析和时段分析。
        *   收入趋势和预测分析（如支持付费服务）。
    *   **智能运营建议：**
        *   基于数据自动生成运营优化建议。
        *   识别高价值用户和流失风险用户。
        *   推荐最优排班和服务配置。

---

#### **4. 核心业务流程**

##### **4.1 用户预约流程 (UML活动图逻辑)**
1.  用户在小程序选择服务和时间点。
2.  点击“立即预约”，调用 `POST /api/bookings`。
3.  **后端服务 (Spring Boot) 开启事务:**
    a. 读取商家的【预约确认模式】配置。
    b. **执行并发控制：** 使用**数据库悲观锁 (`SELECT ... FOR UPDATE`)** 查询并锁定该时间点对应的资源（如时间槽），确保其可用。
    c. 若资源不可用（已被锁定或预约），事务回滚，返回错误信息。
    d. 若资源可用，创建预约单，设置初始状态：
        *   若为“自动确认”模式，状态设为 `CONFIRMED`。
        *   若为“人工确认”模式，状态设为 `PENDING`。
    e. 更新资源状态（如将时间槽标记为已占用）。
    f. **提交事务。**
4.  后端向小程序返回成功的响应，包含最终的预约单状态。
5.  小程序根据返回的状态，向用户展示相应的提示信息。

---

#### **5. 数据模型 (Database Schema)**

*   **User (用户表):** `id`, `open_id`, `nickname`, `avatar_url`, `phone`, `status` (NORMAL, BLOCKED), `is_admin` (boolean)
*   **Service (服务项目表):** `id`, `name`, `...`, `status` (DRAFT, PUBLISHED)
*   **Booking (预约单表):** `id`, `user_id`, `service_id`, `...`, `status` (enum: PENDING, CONFIRMED, COMPLETED, CANCELED_BY_USER, CANCELED_BY_ADMIN)
*   **ShopSetting (店铺设置表):**
    *   `shop_name`, `logo`, `...`
    *   **`booking_rules` (JSONB/TEXT):** 存储结构化规则，如 `{"maxAdvanceDays": 7, "needsAdminConfirmation": false, ...}`
*   **BusinessHours (营业时间表):** `id`, `day_of_week`, `start_time`, `end_time`
*   **(可选优化) TimeSlot (时间槽表):**
    *   `id`, `service_id` (optional), `slot_start_time`, `slot_end_time`, `status` (AVAILABLE, BOOKED)。
    *   **说明:** 可以通过定时任务预生成未来的时间槽，进行预约时直接操作此表，比实时计算更高效，且更容易实现悲观锁。

---

#### **6. 非功能性需求 (Non-functional Requirements)**

*   **性能：** API平均响应时间 < 200ms。核心预约接口，在并发控制下，响应时间 < 500ms。
*   **安全：**
    *   所有需授权API必须经过认证和权限校验。
    *   关键输入进行防SQL注入处理。
    *   Web后台需防止CSRF攻击。
*   **健壮性：**
    *   **【明确】** 必须在后端通过**数据库悲观锁**机制，有效防止同一时间点被超额预约的并发问题。
*   **可用性：** 系统部署后应保证99.9%的可用性。

